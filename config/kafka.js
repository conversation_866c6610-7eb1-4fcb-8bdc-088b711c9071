export default {
  security: {
    protocol: process.env.KAFKA_SECURITY_PROTOCOL || 'PLAIN',
    username: process.env.<PERSON>AFKA_USERNAME,
    password: process.env.KAFKA_PASSWORD,
  },
  brokers: (process.env.KAFKA_BROKERS || 'localhost:9092').split(','),
  connectionTimeout: parseInt(process.env.KAFKA_CONNECTION_TIMEOUT || '30000', 10),
  topics: {
    smsNotification: process.env.KAFKA_TOPIC_SMS || 'SmartAlert.Notification.SMS',
    metrics: process.env.KAFKA_TOPIC_METRICS || 'SmartAlert.Metrics',
  },
};
