import axios from 'axios';
import moment from 'moment-timezone';
import qs from 'qs';
import logger from '../utils/logger.js';

export default class KaleyraSmsService {
  constructor() {
    this.apiKey = process.env.KALEYRA_API_KEY;
    this.sid = process.env.KALEYRA_SID;
    this.senderId = process.env.KALEYRA_SMS_SENDER_ID;
    this.baseUrl =
      process.env.KALEYRA_API_BASE_URL || 'https://api.kaleyra.io/v1';

    if (!this.apiKey || !this.sid || !this.senderId) {
      throw new Error(
        'Missing required Kaleyra SMS configuration. Please check environment variables.',
      );
    }

    // Rate limiting configuration - Kaleyra SMS supports up to 100 recipients per bulk request
    this.rateLimitPerSecond =
      parseInt(process.env.SMS_RATE_LIMIT_PER_SECOND) || 30;
    this.batchSize = parseInt(process.env.SMS_BATCH_SIZE) || 100;
  }

  /**
   * Send SMS notification using Kaleyra bulk SMS API
   * @param {Object} notificationData - The notification data from Kafka message
   * @returns {Promise<Object>} - Response with send results
   */
  async sendNotification(notificationData) {
    const { recipients, content, metadata, transactionId } = notificationData;

    try {
      // Process recipients in batches to respect rate limits
      const batches = this.createBatches(recipients, this.batchSize);
      const allResults = [];

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];

        // Send batch using bulk SMS API
        const batchResult = await this.sendBulkSms(batch, content, metadata);
        allResults.push(batchResult);

        // Add delay between batches to respect rate limit
        if (i < batches.length - 1) {
          await this.delay(1000);
        }
      }

      const totalSent = allResults.reduce(
        (sum, result) => sum + (result.totalCount || 0),
        0,
      );
      const totalFailed = recipients.length - totalSent;

      logger.info(`SMS notification batch completed`, {
        notificationData,
        total: recipients.length,
        sent: totalSent,
        failed: totalFailed,
      });
    } catch (error) {
      logger.error(`Failed to process SMS notification`, {
        notificationData,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Send bulk SMS to multiple recipients
   * Reference: https://developers.kaleyra.io/docs/send-bulk-sms
   */
  async sendBulkSms(recipients, content, metadata) {
    let phoneNumbers = [];
    try {
      // Format phone numbers and create recipient list
      phoneNumbers = recipients
        .map((recipient) => this.formatPhoneNumber(recipient.mobileNumber))
        .filter((phone) => phone !== null);

      if (phoneNumbers.length === 0) {
        throw new Error('No valid phone numbers found in recipients');
      }

      // Prepare SMS content with alert details
      const smsBody = this.makeSMSBody(content, metadata);

      // Prepare request payload according to Kaleyra SMS API
      const payload = {
        to: phoneNumbers.join(','), // Comma-separated phone numbers
        sender: this.senderId,
        type: 'TXN',
        body: smsBody,
        ref: `Alert_${metadata.alertInventoryId}`, // Reference for tracking
        ref1: metadata.siteId,
        template_id: process.env.KALEYRA_TEMPLATE_ID || '1107162434468909790',
      };

      // Make API call to Kaleyra
      const response = await this.makeApiCall('POST', '/messages', payload);

      if (!response.data || !response.data.id) {
        throw new Error('Invalid response structure from Kaleyra SMS API');
      }

      logger.info(`SMS sent successfully to batch`, {
        batchId: response.data.id,
      });

      return {
        batchId: response.data.id,
        sender: response.data.sender,
        type: response.data.type,
        totalCount: response.data.totalCount,
        messageData: response.data.data || [],
        status: 'sent',
      };
    } catch (error) {
      logger.error(`Failed to send SMS batch`, {
        recipients: recipients.map((recipient) => recipient.email),
        phoneNumbers,
        error: error.message,
        errorDetails: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Format phone number to E.164 format
   */
  formatPhoneNumber(phoneNumber) {
    if (!phoneNumber) return null;

    // Remove all non-numeric characters
    let cleaned = phoneNumber.toString().replace(/\D/g, '');

    // Remove leading zeros
    cleaned = cleaned.replace(/^0+/, '');

    // Add country code if not present (defaulting to India +91)
    if (cleaned.length === 10) {
      cleaned = '91' + cleaned;
    }

    // Add + prefix for E.164 format
    return '+' + cleaned;
  }

  /**
   * Format SMS content with alert information
   */
  makeSMSBody(content, metadata) {
    const formattedTimestamp = this.formatTimestamp(metadata.timestamp);

    const body = [
      'Alert from Smart Joules Pvt. Ltd.',
      '',
      `${metadata.alertType} Alert`,
      '',
      `Site: ${metadata.siteName} @Time=${formattedTimestamp}`,
      `Title: ${content.title}`,
      `Description: ${content.body}`,
      '',
      'Check details at https://smartjoules.org/. You are receiving this SMS because you were tagged in this SMS coming from Nudge/JouleRecipe. To stop receiving further SMS you can turn your SMS preference off from the user page at https://smartjoules.org/user/edit. If this SMS is not rendered correctly please contact the PD team for assistance.',
      '- Smart Joule',
    ].join('\n');

    return body;
  }

  /**
   * Format timestamp for display in IST
   */
  formatTimestamp(timestamp) {
    try {
      return moment(timestamp)
        .tz('Asia/Kolkata')
        .format('DD MMM YYYY hh:mm:ss A');
    } catch (error) {
      logger.warn(`Failed to format timestamp: ${error.message}`, {
        timestamp,
      });
      return timestamp;
    }
  }

  /**
   * Make API call to Kaleyra SMS API
   */
  async makeApiCall(method, endpoint, data) {
    const url = `${this.baseUrl}/${this.sid}${endpoint}`;

    try {
      const config = {
        method,
        url,
        headers: {
          'api-key': this.apiKey,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        timeout: 30000, // 30 second timeout
        validateStatus: (status) => status === 200 || status === 202,
      };

      if (method === 'POST') {
        config.data = qs.stringify(data);
      } else if (method === 'GET') {
        config.params = data;
      }

      logger.debug(`📡 Kaleyra SMS API call`, {
        method,
        endpoint,
        templateId: data.template_id,
      });

      const response = await axios(config);
      return response;
    } catch (error) {
      if (error.response) {
        const errorData = error.response.data;
        if (errorData?.error?.message) {
          error.message = errorData.error.message;
        } else if (errorData?.message) {
          error.message = errorData.message;
        } else if (errorData?.code && errorData?.message) {
          error.message = `${errorData.code}: ${errorData.message}`;
        } else if (typeof errorData === 'string') {
          error.message = errorData;
        }
      }

      throw error;
    }
  }

  /**
   * Create batches of recipients
   */
  createBatches(recipients, batchSize) {
    const batches = [];
    for (let i = 0; i < recipients.length; i += batchSize) {
      batches.push(recipients.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Helper function for delay
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
