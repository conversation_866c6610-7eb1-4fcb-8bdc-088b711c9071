{"name": "kaleyra-sms-service", "version": "1.0.0", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@sentry/node": "^7.0.0", "aws-sdk": "^2.1520.0", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.3", "joi": "^17.11.0", "kafkajs": "^2.2.4", "kafkajs-snappy": "^1.1.0", "moment-timezone": "^0.5.43", "nodemailer": "^6.9.4", "qs": "^6.11.2", "snappy": "^7.2.2", "uuid": "^9.0.0", "winston": "^3.9.0", "winston-transport-sentry-node": "^2.7.0"}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^2.0.22"}}